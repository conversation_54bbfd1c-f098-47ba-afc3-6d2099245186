server:
  port: 8088
spring:
  application:
    name: using-common
  cloud:
    nacos:
      discovery:
        # 服务发现
        server-addr: 127.0.0.1:8848
        namespace: c2391875-916d-476c-b72e-f4d9db8f5631  # 服务发现命名空间
      config:
        # 配置中心
        server-addr: 127.0.0.1:8848
        namespace: c2391875-916d-476c-b72e-f4d9db8f5631 # 配置中心命名空间
        file-extension: yaml
        shared-configs:
          - data-id: other-config.yaml
            group: OTHER_GROUP
            refresh: true
# OkHttp配置
okhttp:
  enabled: true
  connect-timeout: 10s
  read-timeout: 30s
  write-timeout: 30s
  call-timeout: 60s
  connection-pool:
    max-idle-connections: 5
    keep-alive-duration: 5m
  retry:
    enabled: true
    max-retries: 3
    retry-interval: 1s
  logging:
    enabled: true
    level: BASIC

# 日志配置
logging:
  level:
    com.example: DEBUG
    lhx.project91.okhttp: DEBUG
    okhttp3: DEBUG





# 全局响应处理器配置
#project91:
#  global-response:
#    enabled: true                    # 启用全局响应处理器
#    exception-handler-enabled: false # 禁用全局异常处理器（避免javax/jakarta包冲突）
#    handle-string-response: false    # 禁用String类型返回值处理（避免转换冲突）
#    handle-void-response: true       # 处理void类型返回值
#    handle-primitive-response: true  # 处理基本数据类型返回值
#    handle-collection-response: true # 处理集合类型返回值
#    auto-trace-id: true             # 自动添加追踪ID
#    trace-id-header: "X-Trace-Id"   # 追踪ID请求头名称
#    add-server-info: true           # 添加服务器信息
#    server-name: "using-common-server" # 服务器名称
#    api-version: "v1.0"             # API版本
#    exclude-packages:               # 排除的包路径
#      - "springfox.documentation"
#      - "org.springframework.boot.actuate"
#    exclude-class-patterns:         # 排除的类名模式
#      - "ErrorController"
#      - "SwaggerController"

